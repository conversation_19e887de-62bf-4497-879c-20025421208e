from typing import Any, Dict, Optional, Union, List
from pathlib import Path
from dotenv import load_dotenv
import os
import json
import yaml
from functools import reduce
import logging

class ConfigManager:
    """
    配置管理器，支持：
    1. 多种配置源（环境变量、JSON、YAML）
    2. 嵌套配置访问
    3. 类型转换
    4. 配置验证
    5. 配置热重载
    """

    def __init__(
        self,
        env_file: Union[str, Path] = ".env",
        config_files: Optional[List[Union[str, Path]]] = None,
        auto_reload: bool = False
    ) -> None:
        """
        初始化配置管理器
        
        Args:
            env_file: 环境变量文件路径
            config_files: 配置文件路径列表（支持.json和.yaml）
            auto_reload: 是否启用配置自动重载
        """
        self.logger = logging.getLogger(__name__)
        self.config: Dict[str, Any] = {}
        self.env_file = Path(env_file)
        self.config_files = [Path(f) for f in (config_files or [])]
        self.auto_reload = auto_reload
        self._file_timestamps: Dict[Path, float] = {}
        
        # 初始化配置
        self._load_config()

    def _set_nested_dict(self, d: Dict[str, Any], keys: list, value: Any) -> None:
        """在嵌套字典中设置值"""
        for key in keys[:-1]:
            d = d.setdefault(key, {})
        d[keys[-1]] = value

    def _load_config(self) -> None:
        """加载所有配置源"""
        self.config.clear()
        
        # 1. 加载配置文件
        for config_file in self.config_files:
            self._load_config_file(config_file)
            if self.auto_reload:
                self._file_timestamps[config_file] = config_file.stat().st_mtime

        # 2. 加载环境变量文件
        if self.env_file.exists():
            load_dotenv(self.env_file)
            if self.auto_reload:
                self._file_timestamps[self.env_file] = self.env_file.stat().st_mtime

        # 3. 加载环境变量（最高优先级）
        self._load_env_vars()

    def _load_config_file(self, file_path: Path) -> None:
        """加载配置文件（支持JSON和YAML）"""
        if not file_path.exists():
            self.logger.warning(f"配置文件不存在: {file_path}")
            return

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.suffix.lower() == '.json':
                    config_data = json.load(f)
                elif file_path.suffix.lower() in ('.yml', '.yaml'):
                    config_data = yaml.safe_load(f)
                else:
                    self.logger.warning(f"不支持的配置文件格式: {file_path}")
                    return

                # 更新配置
                self._merge_config(config_data)

        except Exception as e:
            self.logger.error(f"加载配置文件失败 {file_path}: {str(e)}")

    def _merge_config(self, new_config: Dict[str, Any]) -> None:
        """递归合并配置字典"""
        def merge_dicts(d1: Dict[str, Any], d2: Dict[str, Any]) -> Dict[str, Any]:
            for k, v in d2.items():
                if k in d1 and isinstance(d1[k], dict) and isinstance(v, dict):
                    merge_dicts(d1[k], v)
                else:
                    d1[k] = v
            return d1

        self.config = merge_dicts(self.config, new_config)

    def _load_env_vars(self) -> None:
        """加载环境变量，支持嵌套结构，统一转换为小写"""
        for key, value in os.environ.items():
            # 将键转换为小写
            key = key.lower()
            if '.' in key:
                # 处理嵌套配置
                key_parts = key.split('.')
                self._set_nested_dict(self.config, key_parts, value)
            else:
                # 普通键值对
                self.config[key] = value

    def check_reload(self) -> bool:
        """检查配置文件是否需要重新加载"""
        if not self.auto_reload:
            return False

        needs_reload = False
        for file_path, last_mtime in self._file_timestamps.items():
            if file_path.exists():
                current_mtime = file_path.stat().st_mtime
                if current_mtime > last_mtime:
                    needs_reload = True
                    break

        if needs_reload:
            self._load_config()
            return True
        return False

    def get(self, key: str, default: Any = None) -> Any:
        """
        获取配置值，支持点号表示法，大小写不敏感
        
        Args:
            key: 配置键名（如 'database.host' 或 'DATABASE.HOST'）
            default: 默认值
        """
        if self.auto_reload:
            self.check_reload()

        # 将键转换为小写
        key = key.lower()

        if '.' not in key:
            return self.config.get(key, default)
        
        try:
            value = reduce(lambda d, k: d[k], key.split('.'), self.config)
            return value
        except (KeyError, TypeError):
            return default

    def get_int(self, key: str, default: int = 0) -> int:
        """获取整数类型配置值"""
        try:
            return int(self.get(key, default))
        except (TypeError, ValueError):
            return default

    def get_float(self, key: str, default: float = 0.0) -> float:
        """获取浮点数类型配置值"""
        try:
            return float(self.get(key, default))
        except (TypeError, ValueError):
            return default

    def get_bool(self, key: str, default: bool = False) -> bool:
        """获取布尔类型配置值"""
        value = self.get(key, default)
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ('true', '1', 'yes', 'on', 't')
        return bool(value)

    def get_list(self, key: str, default: Optional[list] = None) -> list:
        """获取列表类型配置值"""
        value = self.get(key, default or [])
        if isinstance(value, str):
            return [item.strip() for item in value.split(',')]
        if isinstance(value, list):
            return value
        return [value]

    def get_dict(self, key: str, default: Optional[dict] = None) -> dict:
        """获取字典类型配置值"""
        value = self.get(key, default or {})
        return value if isinstance(value, dict) else default or {}

    def set(self, key: str, value: Any) -> None:
        """设置配置值，支持点号表示法"""
        if '.' in key:
            keys = key.split('.')
            self._set_nested_dict(self.config, keys, value)
        else:
            self.config[key] = value

    def has(self, key: str) -> bool:
        """检查配置键是否存在"""
        try:
            return self.get(key) is not None
        except:
            return False

    @property
    def all(self) -> Dict[str, Any]:
        """获取所有配置的副本"""
        if self.auto_reload:
            self.check_reload()
        return self.config.copy()

    def __getattr__(self, name: str) -> Any:
        """支持通过属性方式访问配置"""
        return self.get(name)

    def __getitem__(self, key: str) -> Any:
        """支持通过字典方式访问配置"""
        return self.get(key)


# 使用示例
def main() -> None:

    current_dir = os.path.dirname(os.path.abspath(__file__))
    root_dir = os.path.dirname(current_dir)
    # 设置一些测试环境变量
    os.environ['DATABASE.HOST'] = 'localhost'
    os.environ['DATABASE.PORT'] = '5432'
    os.environ['DATABASE.CREDENTIALS.USERNAME'] = 'admin'
    os.environ['APP.DEBUG'] = 'true'
    os.environ['SIMPLE_KEY'] = 'simple_value'

    # 初始化配置管理器
    config = ConfigManager(
        env_file=os.path.join(root_dir, ".env"),
        config_files=[os.path.join(root_dir, "assets/config.json")],
        auto_reload=True
    )

    # 获取各种类型的配置
    db_host = config.get('DATABASE.HOST')
    db_port = config.get_int('DATABASE.PORT')
    debug_mode = config.get_bool('APP.DEBUG')
    db_creds = config.get_dict('DATABASE.CREDENTIALS')
    
    # 输出配置
    print(f"数据库主机: {db_host}")
    print(f"数据库端口: {db_port}")
    print(f"调试模式: {debug_mode}")
    print(f"数据库凭证: {db_creds}")
    print(f"所有配置: {config.all}")

if __name__ == '__main__':
    main()
