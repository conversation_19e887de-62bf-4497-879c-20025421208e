"""
浏览器指纹生成器
用于生成随机的浏览器指纹，模拟真实浏览器环境，避免自动化检测
"""

import random
from typing import Dict
from playwright.async_api import Page, BrowserContext


class BrowserFingerprint:
    """浏览器指纹生成器，用于模拟真实浏览器环境"""
    
    def __init__(self):
        # 常见的用户代理字符串池
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36",
            "Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:131.0) Gecko/20100101 Firefox/131.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
        ]
        
        # 常见的屏幕分辨率
        self.screen_resolutions = [
            {"width": 1920, "height": 1080},
            {"width": 1366, "height": 768},
            {"width": 1536, "height": 864},
            {"width": 1440, "height": 900},
            {"width": 1280, "height": 720},
            {"width": 1600, "height": 900},
            {"width": 2560, "height": 1440},
            {"width": 1680, "height": 1050},
            {"width": 1280, "height": 800},
            {"width": 1024, "height": 768},
        ]
        
        # 常见的时区
        self.timezones = [
            "America/New_York",
            "America/Los_Angeles", 
            "America/Chicago",
            "Europe/London",
            "Europe/Berlin",
            "Asia/Tokyo",
            "Asia/Shanghai",
            "Australia/Sydney",
            "America/Toronto",
            "Europe/Paris",
        ]
        
        # 常见的语言设置
        self.locales = [
            # "en-US",
            # "en-GB", 
            "zh-CN",
            # "ja-JP",
            # "de-DE",
            # "fr-FR",
            # "es-ES",
            # "pt-BR",
            # "ru-RU",
            # "ko-KR",
        ]
    
    def generate_fingerprint(self) -> Dict:
        """生成随机的浏览器指纹"""
        resolution = random.choice(self.screen_resolutions)
        
        # 生成视口大小（通常比屏幕分辨率小一些）
        viewport_width = resolution["width"] - random.randint(0, 200)
        viewport_height = resolution["height"] - random.randint(100, 300)
        
        fingerprint = {
            "user_agent": random.choice(self.user_agents),
            "viewport": {
                "width": max(viewport_width, 800),  # 确保最小宽度
                "height": max(viewport_height, 600)  # 确保最小高度
            },
            "screen": resolution,
            "timezone_id": random.choice(self.timezones),
            "locale": random.choice(self.locales),
            "device_scale_factor": random.choice([1, 1.25, 1.5, 2]),
            "has_touch": random.choice([True, False]),
            "color_depth": random.choice([24, 32]),
            "reduced_motion": random.choice(["reduce", "no-preference"]),
            "forced_colors": random.choice(["active", "none"]),
        }
        
        return fingerprint
    
    def get_browser_options(self, fingerprint: Dict) -> Dict:
        """根据指纹生成浏览器启动选项"""
        return {
            "headless": False,  # 设置为True可以在无头模式下运行
            "args": [
                "--disable-blink-features=AutomationControlled",
                "--disable-features=VizDisplayCompositor",
                "--disable-web-security",
                "--disable-features=TranslateUI",
                "--disable-ipc-flooding-protection",
                f"--user-agent={fingerprint['user_agent']}",
            ]
        }
    
    def get_context_options(self, fingerprint: Dict, proxy_config: Dict = None) -> Dict:
        """根据指纹生成浏览器上下文选项"""
        context_options = {
            "viewport": fingerprint["viewport"],
            "user_agent": fingerprint["user_agent"],
            "timezone_id": fingerprint["timezone_id"],
            "locale": fingerprint["locale"],
            "device_scale_factor": fingerprint["device_scale_factor"],
            "has_touch": fingerprint["has_touch"],
            "color_scheme": "light",
            "reduced_motion": fingerprint["reduced_motion"],
            "forced_colors": fingerprint["forced_colors"],
            "screen": fingerprint["screen"],
            "extra_http_headers": {
                "Accept-Language": f"{fingerprint['locale']},en;q=0.9"
            }
        }
        
        # 如果提供了代理配置，添加到上下文选项中
        if proxy_config:
            context_options["proxy"] = proxy_config
            
        return context_options

    def get_camoufox_options(self, fingerprint: Dict) -> Dict:
        """根据指纹生成camoufox启动选项"""
        try:
            from camoufox import DefaultAddons
            # 排除所有默认插件以避免manifest.json问题
            exclude_addons = [getattr(DefaultAddons, attr) for attr in dir(DefaultAddons) if not attr.startswith('_')]
        except (ImportError, AttributeError):
            # 如果导入失败或属性不存在，使用空列表
            exclude_addons = []

        camoufox_options = {
            "headless": False,
            "humanize": True,  # 启用人性化鼠标移动
            "locale": fingerprint["locale"],
            "os": "windows",  # 固定使用windows
            "geoip": True,  # 暂时禁用geoip避免问题
            "exclude_addons": exclude_addons,  # 排除所有默认插件
        }

        return camoufox_options

    async def inject_anti_detection_script(self, context: BrowserContext, fingerprint: Dict):
        """注入反检测脚本"""
        await context.add_init_script(f"""
            // 移除webdriver属性
            Object.defineProperty(navigator, 'webdriver', {{
                get: () => undefined,
            }});
            
            // 伪造chrome对象
            window.chrome = {{
                runtime: {{}},
                loadTimes: function() {{}},
                csi: function() {{}},
                app: {{}}
            }};
            
            // 伪造插件
            Object.defineProperty(navigator, 'plugins', {{
                get: () => [1, 2, 3, 4, 5],
            }});
            
            // 伪造语言
            Object.defineProperty(navigator, 'languages', {{
                get: () => ['{fingerprint['locale']}', 'en'],
            }});
            
            // 伪造平台
            Object.defineProperty(navigator, 'platform', {{
                get: () => 'Win32',
            }});
            
            // 移除自动化相关属性
            delete navigator.__proto__.webdriver;
            
            // 伪造权限查询
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({{ state: Notification.permission }}) :
                    originalQuery(parameters)
            );
            
            // 伪造屏幕信息
            Object.defineProperty(screen, 'width', {{
                get: () => {fingerprint['screen']['width']},
            }});
            Object.defineProperty(screen, 'height', {{
                get: () => {fingerprint['screen']['height']},
            }});
            Object.defineProperty(screen, 'colorDepth', {{
                get: () => {fingerprint['color_depth']},
            }});
        """)
    
    async def simulate_human_behavior(self, page: Page):
        """模拟人类行为"""
        # 随机移动鼠标
        await page.mouse.move(
            random.randint(100, 800), 
            random.randint(100, 600)
        )
        
        # 随机等待
        await page.wait_for_timeout(random.randint(500, 2000))
        
        # 随机滚动
        await page.evaluate(f"""
            window.scrollTo({{
                top: {random.randint(0, 500)},
                behavior: 'smooth'
            }});
        """)
        
        await page.wait_for_timeout(random.randint(300, 1000))
    
    async def human_like_fill(self, page: Page, selector: str, value: str, delay_range: tuple = (200, 500)):
        """人性化填写输入框"""
        # 先点击输入框
        await page.click(selector)
        # 随机等待
        await page.wait_for_timeout(random.randint(*delay_range))
        # 填写内容
        await page.fill(selector, value)
        # 再次随机等待
        await page.wait_for_timeout(random.randint(100, 300))
