import requests
import json
import os
from typing import Dict, Any, List

mail_headers = {
    "accept": "*/*",
    "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "authorization": "Bearer 9ba4c11ce3a9235a34b3481795485dec",
    "content-type": "application/json",
}


async def get_email_content(email: str) -> str:
    """
    获取邮件内容
    Args:
        email: 邮箱地址
    Returns:
        str: 邮件内容
    Raises:
        Exception: 当API请求失败时抛出异常
    """
    response = requests.post(
        "https://godmailyi-msmail.hf.space/api/mail/new",
        headers=mail_headers,
        json={"email": email},
    )
    if not response.ok:
        data = response.json()
        raise Exception(f"获取验证码失败: {data.get('error')}")

    data = response.json()
    return data["text"]
 
 
 
# 读取并解析 microsoft_died.json 文件
def load_accounts() -> List[Dict[str, Any]]:
    """
    从 data/microsoft_died.json 文件中加载账户数据
    Returns:
        List[Dict[str, Any]]: 账户列表
    """
    try:
        file_path = os.path.join(os.getcwd(), 'data', 'microsoft_died.json')
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"文件未找到: {file_path}")
        return []
    except json.JSONDecodeError as e:
        print(f"JSON解析错误: {e}")
        return []
    except Exception as e:
        print(f"加载账户数据时发生错误: {e}")
        return []

# 加载账户数据
accounts = load_accounts()